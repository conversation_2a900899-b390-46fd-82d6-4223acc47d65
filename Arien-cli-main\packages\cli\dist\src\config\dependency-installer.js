/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { promisify } from 'util';
import { exec } from 'child_process';
const execAsync = promisify(exec);
/**
 * Handles automatic installation of missing dependencies for MCP servers
 */
export class DependencyInstaller {
    static INSTALLATION_OPTIONS = {
        'uvx': [
            {
                command: 'uvx',
                description: 'Install uv (includes uvx) via pip',
                installCommand: 'pip install uv',
                verifyCommand: 'uvx --version',
            },
            {
                command: 'uvx',
                description: 'Install uv (includes uvx) via PowerShell (Windows)',
                installCommand: 'powershell -c "irm https://astral.sh/uv/install.ps1 | iex"',
                verifyCommand: 'uvx --version',
            },
            {
                command: 'uvx',
                description: 'Install uv (includes uvx) via curl (Unix/macOS)',
                installCommand: 'curl -LsSf https://astral.sh/uv/install.sh | sh',
                verifyCommand: 'uvx --version',
            },
            {
                command: 'uvx',
                description: 'Install uv (includes uvx) via winget (Windows)',
                installCommand: 'winget install --id=astral-sh.uv -e',
                verifyCommand: 'uvx --version',
            },
            {
                command: 'uvx',
                description: 'Install uv (includes uvx) via scoop (Windows)',
                installCommand: 'scoop install uv',
                verifyCommand: 'uvx --version',
            },
        ],
        'npx': [
            {
                command: 'npx',
                description: 'Install Node.js (includes npx) - requires manual installation',
                installCommand: 'echo "Please install Node.js from https://nodejs.org/"',
                verifyCommand: 'npx --version',
            },
        ],
        'git': [
            {
                command: 'git',
                description: 'Install Git - requires manual installation',
                installCommand: 'echo "Please install Git from https://git-scm.com/"',
                verifyCommand: 'git --version',
            },
        ],
    };
    /**
     * Attempts to automatically install a missing dependency
     */
    static async installDependency(command, interactive = false) {
        const options = this.INSTALLATION_OPTIONS[command];
        if (!options || options.length === 0) {
            return {
                success: false,
                command,
                message: `No automatic installation available for '${command}'`,
            };
        }
        // Sort options by platform preference
        const sortedOptions = this.sortOptionsByPlatform(options);
        // Try each installation option
        for (const option of sortedOptions) {
            try {
                console.log(`🔧 Attempting to install ${option.description}...`);
                // Skip manual installation commands in non-interactive mode
                if (!interactive && option.installCommand.includes('echo "Please install')) {
                    console.log(`⚠️ ${option.description} requires manual installation`);
                    continue;
                }
                // Execute the installation command
                const { stdout, stderr } = await execAsync(option.installCommand, {
                    timeout: 60000, // 1 minute timeout
                    env: { ...process.env, PATH: process.env.PATH }
                });
                console.log(`📦 Installation output: ${stdout}`);
                if (stderr) {
                    console.warn(`⚠️ Installation warnings: ${stderr}`);
                }
                // Verify the installation with retry
                try {
                    // Wait a bit for the installation to complete and PATH to be updated
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    // Try verification with updated PATH
                    await execAsync(option.verifyCommand, {
                        timeout: 10000,
                        env: {
                            ...process.env,
                            PATH: this.getUpdatedPath()
                        }
                    });
                    return {
                        success: true,
                        command,
                        message: `Successfully installed ${option.description}`,
                    };
                }
                catch (verifyError) {
                    console.warn(`❌ Installation verification failed: ${verifyError}`);
                    // Try one more time with a longer delay for Windows
                    if (process.platform === 'win32') {
                        try {
                            await new Promise(resolve => setTimeout(resolve, 3000));
                            await execAsync(option.verifyCommand, {
                                timeout: 10000,
                                env: {
                                    ...process.env,
                                    PATH: this.getUpdatedPath()
                                }
                            });
                            return {
                                success: true,
                                command,
                                message: `Successfully installed ${option.description} (after retry)`,
                            };
                        }
                        catch (retryError) {
                            console.warn(`❌ Installation verification retry failed: ${retryError}`);
                        }
                    }
                    continue;
                }
            }
            catch (_error) {
                console.warn(`❌ Installation failed for ${option.description}: ${_error}`);
                continue;
            }
        }
        return {
            success: false,
            command,
            message: `Failed to install '${command}' using available methods`,
            error: 'All installation attempts failed',
        };
    }
    /**
     * Checks if a dependency can be automatically installed
     */
    static canAutoInstall(command) {
        const options = this.INSTALLATION_OPTIONS[command];
        return options && options.some(option => !option.installCommand.includes('echo "Please install'));
    }
    /**
     * Gets installation instructions for a dependency
     */
    static getInstallationInstructions(command) {
        const options = this.INSTALLATION_OPTIONS[command];
        if (!options) {
            return [`No installation instructions available for '${command}'`];
        }
        return options.map(option => option.description);
    }
    /**
     * Attempts to install multiple dependencies
     */
    static async installMultipleDependencies(commands, interactive = false) {
        const results = {};
        for (const command of commands) {
            console.log(`\n🔍 Checking dependency: ${command}`);
            // First check if it's already installed
            try {
                await execAsync(`${command} --version`, { timeout: 5000 });
                results[command] = {
                    success: true,
                    command,
                    message: `${command} is already installed`,
                };
                console.log(`✅ ${command} is already available`);
                continue;
            }
            catch (_error) {
                // Not installed, try to install it
                console.log(`❌ ${command} is not available, attempting installation...`);
            }
            // Attempt installation
            const result = await this.installDependency(command, interactive);
            results[command] = result;
            if (result.success) {
                console.log(`✅ ${result.message}`);
            }
            else {
                console.warn(`❌ ${result.message}`);
            }
        }
        return results;
    }
    /**
     * Provides a summary of installation results
     */
    static summarizeInstallationResults(results) {
        const successful = Object.values(results).filter(r => r.success);
        const failed = Object.values(results).filter(r => !r.success);
        console.log(`\n📊 Dependency Installation Summary:`);
        console.log(`   ✅ Successful: ${successful.length}`);
        console.log(`   ❌ Failed: ${failed.length}`);
        if (failed.length > 0) {
            console.log(`\n❌ Failed installations:`);
            failed.forEach(result => {
                console.log(`   • ${result.command}: ${result.message}`);
            });
            console.log(`\n💡 Manual installation may be required for some dependencies.`);
            console.log(`   Please refer to the official documentation for installation instructions.`);
        }
    }
    /**
     * Sorts installation options by platform preference
     */
    static sortOptionsByPlatform(options) {
        const platform = process.platform;
        return [...options].sort((a, b) => {
            // Prioritize platform-specific options
            if (platform === 'win32') {
                // Windows: prefer PowerShell, winget, scoop, then pip, then others
                const aScore = this.getWindowsScore(a.description);
                const bScore = this.getWindowsScore(b.description);
                return bScore - aScore;
            }
            else if (platform === 'darwin') {
                // macOS: prefer curl, then pip, then others
                const aScore = this.getMacScore(a.description);
                const bScore = this.getMacScore(b.description);
                return bScore - aScore;
            }
            else {
                // Linux/Unix: prefer curl, then pip, then others
                const aScore = this.getLinuxScore(a.description);
                const bScore = this.getLinuxScore(b.description);
                return bScore - aScore;
            }
        });
    }
    static getWindowsScore(description) {
        if (description.includes('PowerShell'))
            return 100;
        if (description.includes('winget'))
            return 90;
        if (description.includes('scoop'))
            return 80;
        if (description.includes('pip'))
            return 70;
        if (description.includes('curl'))
            return 20;
        return 50;
    }
    static getMacScore(description) {
        if (description.includes('curl'))
            return 100;
        if (description.includes('pip'))
            return 80;
        if (description.includes('PowerShell'))
            return 10;
        if (description.includes('winget'))
            return 5;
        if (description.includes('scoop'))
            return 5;
        return 50;
    }
    static getLinuxScore(description) {
        if (description.includes('curl'))
            return 100;
        if (description.includes('pip'))
            return 80;
        if (description.includes('PowerShell'))
            return 10;
        if (description.includes('winget'))
            return 5;
        if (description.includes('scoop'))
            return 5;
        return 50;
    }
    /**
     * Gets an updated PATH that includes common installation directories
     */
    static getUpdatedPath() {
        const currentPath = process.env.PATH || '';
        const additionalPaths = [];
        if (process.platform === 'win32') {
            // Common Windows installation paths
            additionalPaths.push(`${process.env.USERPROFILE}\\.local\\bin`, `${process.env.LOCALAPPDATA}\\Programs\\uv\\Scripts`, `${process.env.APPDATA}\\Python\\Scripts`, 'C:\\Program Files\\uv\\Scripts');
        }
        else {
            // Common Unix/Linux/macOS installation paths
            additionalPaths.push(`${process.env.HOME}/.local/bin`, `${process.env.HOME}/.cargo/bin`, '/usr/local/bin', '/opt/homebrew/bin');
        }
        const separator = process.platform === 'win32' ? ';' : ':';
        return [currentPath, ...additionalPaths].join(separator);
    }
}
//# sourceMappingURL=dependency-installer.js.map