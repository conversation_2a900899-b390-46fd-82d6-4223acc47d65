/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Test suite for MCP server configuration and dependency handling
 */
export declare class MCPServerTest {
    /**
     * Tests the MCP server registry functionality
     */
    static testMCPServerRegistry(): Promise<void>;
    /**
     * Tests the dependency installer functionality
     */
    static testDependencyInstaller(): Promise<void>;
    /**
     * Tests the overall MCP server integration
     */
    static testMCPServerIntegration(): Promise<void>;
    /**
     * Runs all tests
     */
    static runAllTests(): Promise<void>;
    /**
     * Provides a summary of the current MCP server status
     */
    static provideSummary(): Promise<void>;
}
