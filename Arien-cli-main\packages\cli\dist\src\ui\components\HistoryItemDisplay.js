import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { UserMessage } from './messages/UserMessage.js';
import { UserShellMessage } from './messages/UserShellMessage.js';
import { ArienMessage } from './messages/ArienMessage.js';
import { InfoMessage } from './messages/InfoMessage.js';
import { ErrorMessage } from './messages/ErrorMessage.js';
import { ToolGroupMessage } from './messages/ToolGroupMessage.js';
import { ArienMessageContent } from './messages/ArienMessageContent.js';
import { CompressionMessage } from './messages/CompressionMessage.js';
import { Box } from 'ink';
import { AboutBox } from './AboutBox.js';
import { StatsDisplay } from './StatsDisplay.js';
import { SessionSummaryDisplay } from './SessionSummaryDisplay.js';
export const HistoryItemDisplay = ({ item, availableTerminalHeight, terminalWidth, isPending, config, isFocused = true, }) => (_jsxs(Box, { flexDirection: "column", children: [item.type === 'user' && _jsx(UserMessage, { text: item.text }), item.type === 'user_shell' && _jsx(UserShellMessage, { text: item.text }), item.type === 'arien' && (_jsx(ArienMessage, { text: item.text, isPending: isPending, availableTerminalHeight: availableTerminalHeight, terminalWidth: terminalWidth })), item.type === 'arien_content' && (_jsx(ArienMessageContent, { text: item.text, isPending: isPending, availableTerminalHeight: availableTerminalHeight, terminalWidth: terminalWidth })), item.type === 'info' && _jsx(InfoMessage, { text: item.text }), item.type === 'error' && _jsx(ErrorMessage, { text: item.text }), item.type === 'about' && (_jsx(AboutBox, { cliVersion: item.cliVersion, osVersion: item.osVersion, sandboxEnv: item.sandboxEnv, modelVersion: item.modelVersion, selectedAuthType: item.selectedAuthType, gcpProject: item.gcpProject })), item.type === 'stats' && (_jsx(StatsDisplay, { stats: item.stats, lastTurnStats: item.lastTurnStats, duration: item.duration })), item.type === 'quit' && (_jsx(SessionSummaryDisplay, { stats: item.stats, duration: item.duration })), item.type === 'tool_group' && (_jsx(ToolGroupMessage, { toolCalls: item.tools, groupId: item.id, availableTerminalHeight: availableTerminalHeight, terminalWidth: terminalWidth, config: config, isFocused: isFocused })), item.type === 'compression' && (_jsx(CompressionMessage, { compression: item.compression }))] }, item.id));
//# sourceMappingURL=HistoryItemDisplay.js.map