import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Box, Text } from 'ink';
import { useOverflowState } from '../contexts/OverflowContext.js';
import { useStreamingContext } from '../contexts/StreamingContext.js';
import { StreamingState } from '../types.js';
import { Colors } from '../colors.js';
export const ShowMoreLines = ({ constrainHeight }) => {
    const overflowState = useOverflowState();
    const streamingState = useStreamingContext();
    if (overflowState === undefined ||
        overflowState.overflowingIds.size === 0 ||
        !constrainHeight ||
        !(streamingState === StreamingState.Idle ||
            streamingState === StreamingState.WaitingForConfirmation)) {
        return null;
    }
    const overflowCount = overflowState.overflowingIds.size;
    return (_jsxs(Box, { marginTop: 0, flexDirection: "row", children: [_jsx(Text, { color: Colors.AccentYellow, children: "\u25BC " }), _jsxs(Text, { color: Colors.Gray, wrap: "truncate", children: [overflowCount, " item", overflowCount > 1 ? 's' : '', " hidden \u00B7 Press"] }), _jsx(Text, { color: Colors.AccentYellow, bold: true, children: " Ctrl+S " }), _jsx(Text, { color: Colors.Gray, children: "to show all" })] }));
};
//# sourceMappingURL=ShowMoreLines.js.map