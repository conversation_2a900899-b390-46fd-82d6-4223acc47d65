{"version": 3, "file": "Stats.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/Stats.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAatC,4BAA4B;AAE5B;;GAEG;AACH,MAAM,CAAC,MAAM,OAAO,GAIf,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CACrC,MAAC,GAAG,IAAC,cAAc,EAAC,eAAe,EAAC,GAAG,EAAE,CAAC,aACxC,KAAC,IAAI,IAAC,KAAK,EAAE,MAAM,CAAC,SAAS,YAAG,KAAK,GAAQ,EAC7C,KAAC,IAAI,IAAC,KAAK,EAAE,UAAU,YAAG,KAAK,GAAQ,IACnC,CACP,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAMnB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,GAAG,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC/D,MAAM,aAAa,GACjB,YAAY,IAAI,KAAK,CAAC,WAAW,GAAG,CAAC;QACnC,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;QAC5G,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;IAE1C,MAAM,WAAW,GACf,YAAY,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;IAE1E,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAE,KAAK,aACtC,KAAC,IAAI,IAAC,IAAI,kBAAE,KAAK,GAAQ,EACzB,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aAEvC,KAAC,OAAO,IACN,KAAK,EAAC,cAAc,EACpB,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,GACzC,EACF,KAAC,OAAO,IACN,KAAK,EAAC,eAAe,EACrB,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,cAAc,EAAE,GAC1C,EACD,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI,CAC1B,KAAC,OAAO,IACN,KAAK,EAAC,iBAAiB,EACvB,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,cAAc,EAAE,GAC3C,CACH,EACD,KAAC,OAAO,IACN,KAAK,EAAC,iBAAiB,EACvB,KAAK,EAAE,KAAK,CAAC,cAAc,CAAC,cAAc,EAAE,GAC5C,EACD,KAAK,CAAC,YAAY,GAAG,CAAC,IAAI,CACzB,KAAC,OAAO,IACN,KAAK,EAAC,eAAe,EACrB,KAAK,EAAE,aAAa,EACpB,UAAU,EAAE,WAAW,GACvB,CACH,EAED,KAAC,GAAG,IACF,SAAS,EAAE,IAAI,EACf,UAAU,EAAE,KAAK,EACjB,WAAW,EAAE,KAAK,EAClB,YAAY,EAAE,KAAK,EACnB,WAAW,EAAC,QAAQ,GACpB,EACF,KAAC,OAAO,IACN,KAAK,EAAC,cAAc,EACpB,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,GACzC,EACD,QAAQ,IACL,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAGtB,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAC9B,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAE,KAAK,aACtC,KAAC,IAAI,IAAC,IAAI,+BAAgB,EAC1B,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aACvC,KAAC,OAAO,IAAC,KAAK,EAAC,UAAU,EAAC,KAAK,EAAE,OAAO,GAAI,EAC5C,KAAC,OAAO,IAAC,KAAK,EAAC,WAAW,EAAC,KAAK,EAAE,QAAQ,GAAI,IAC1C,IACF,CACP,CAAC"}