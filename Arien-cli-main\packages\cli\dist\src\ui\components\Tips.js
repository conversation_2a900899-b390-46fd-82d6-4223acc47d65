import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const Tips = ({ config }) => {
    const arienMdFileCount = config.getArienMdFileCount();
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, paddingLeft: 1, children: [_jsx(Box, { marginBottom: 0, children: _jsx(Text, { color: Colors.AccentCyan, bold: true, children: "Quick Start Tips" }) }), _jsxs(Box, { flexDirection: "column", paddingLeft: 2, children: [_jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { color: Colors.Gray, children: "\u2022" }), " Ask questions, edit files, or run commands"] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { color: Colors.Gray, children: "\u2022" }), " Be specific for the best results"] }), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { color: Colors.Gray, children: "\u2022" }), " Built-in tools available: file operations, web search, browser automation, and more"] }), arienMdFileCount === 0 && (_jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { color: Colors.Gray, children: "\u2022" }), " Create", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "ARIEN.md" }), ' ', "files to customize your interactions"] })), _jsxs(Text, { color: Colors.Foreground, children: [_jsx(Text, { color: Colors.Gray, children: "\u2022" }), " Type", ' ', _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "/help" }), ' ', "for commands and shortcuts"] })] })] }));
};
//# sourceMappingURL=Tips.js.map