import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
export const UpdateNotification = ({ message }) => (_jsx(Box, { borderStyle: "round", borderColor: Colors.AccentYellow, paddingX: 1, paddingY: 0, marginY: 1, children: _jsxs(Box, { flexDirection: "row", children: [_jsx(Text, { color: Colors.AccentYellow, bold: true, children: "Update: " }), _jsx(Text, { color: Colors.Foreground, children: message })] }) }));
//# sourceMappingURL=UpdateNotification.js.map