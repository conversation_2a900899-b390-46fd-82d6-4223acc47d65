import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { Colors } from '../../colors.js';
export const ArienMessage = ({ text, isPending, availableTerminalHeight, terminalWidth, }) => {
    // Calculate available width for content with left border indicator
    const contentWidth = Math.max(20, terminalWidth - 4);
    return (_jsx(Box, { flexDirection: "column", marginTop: 1, marginBottom: 2, children: _jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { marginRight: 1, children: _jsx(Text, { color: Colors.AccentPurple, children: "\u2502" }) }), _jsx(Box, { flexGrow: 1, paddingX: 2, paddingY: 1, borderStyle: "round", borderColor: Colors.Gray, children: _jsx(MarkdownDisplay, { text: text, isPending: isPending, availableTerminalHeight: availableTerminalHeight, terminalWidth: contentWidth }) })] }) }));
};
//# sourceMappingURL=ArienMessage.js.map