import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { Colors } from '../../colors.js';
/*
 * Arien message content is a semi-hacked component. The intention is to represent a partial
 * of ArienMessage and is only used when a response gets too long. In that instance messages
 * are split into multiple ArienMessageContent's to enable the root <Static> component in
 * App.tsx to be as performant as humanly possible.
 */
export const ArienMessageContent = ({ text, isPending, availableTerminalHeight, terminalWidth, }) => {
    // Calculate available width for content with left border indicator
    const contentWidth = Math.max(20, terminalWidth - 4);
    return (
    // Use the same styling as ArienMessage for consistent alignment
    _jsx(Box, { flexDirection: "column", children: _jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { marginRight: 1, children: _jsx(Text, { color: Colors.AccentPurple, children: "\u2502" }) }), _jsx(Box, { flexGrow: 1, paddingX: 2, paddingY: 1, borderStyle: "round", borderColor: Colors.Gray, children: _jsx(MarkdownDisplay, { text: text, isPending: isPending, availableTerminalHeight: availableTerminalHeight, terminalWidth: contentWidth }) })] }) }));
};
//# sourceMappingURL=ArienMessageContent.js.map