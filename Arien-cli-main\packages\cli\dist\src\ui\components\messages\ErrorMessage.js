import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';
export const ErrorMessage = ({ text }) => {
    const prefix = '✗ ';
    const prefixWidth = prefix.length;
    // Extract error type if present (e.g., "TypeError:", "SyntaxError:")
    const errorMatch = text.match(/^(\w+Error):\s*(.+)$/);
    const errorType = errorMatch ? errorMatch[1] : null;
    const errorDetail = errorMatch ? errorMatch[2] : text;
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { width: prefixWidth, children: _jsx(Text, { color: Colors.AccentRed, bold: true, children: prefix }) }), _jsxs(Box, { flexGrow: 1, flexDirection: "column", children: [errorType && (_jsx(Text, { color: Colors.AccentRed, bold: true, children: errorType })), _jsx(Text, { wrap: "wrap", color: Colors.AccentRed, children: errorDetail })] })] }), _jsx(Box, { marginTop: 0, paddingLeft: prefixWidth, children: _jsx(Text, { color: Colors.Gray, dimColor: true, children: '─'.repeat(Math.min(errorDetail.length, 50)) }) })] }));
};
//# sourceMappingURL=ErrorMessage.js.map