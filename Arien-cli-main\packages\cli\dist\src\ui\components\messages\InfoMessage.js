import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';
export const InfoMessage = ({ text }) => {
    const prefix = '';
    const prefixWidth = prefix.length;
    // Detect important info patterns
    const isImportant = text.toLowerCase().includes('success') ||
        text.toLowerCase().includes('complete') ||
        text.toLowerCase().includes('refresh');
    // Choose appropriate color based on content
    const textColor = isImportant ? Colors.AccentGreen : Colors.Foreground;
    const prefixColor = isImportant ? Colors.AccentGreen : Colors.AccentCyan;
    return (_jsxs(Box, { flexDirection: "row", marginTop: 1, marginBottom: 0, children: [_jsx(Box, { width: prefixWidth, children: _jsx(Text, { color: prefixColor, bold: true, children: prefix }) }), _jsx(Box, { flexGrow: 1, children: _jsx(Text, { wrap: "wrap", color: textColor, children: text }) })] }));
};
//# sourceMappingURL=InfoMessage.js.map