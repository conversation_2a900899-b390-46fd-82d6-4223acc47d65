import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/* eslint-disable react/prop-types */
import React, { useMemo, useCallback } from 'react';
import { Box, Text } from 'ink';
import { ToolCallStatus } from '../../types.js';
import { Diff<PERSON>ender<PERSON> } from './DiffRenderer.js';
import { Colors } from '../../colors.js';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { ArienRespondingSpinner } from '../ArienRespondingSpinner.js';
import { MaxSizedBox } from '../shared/MaxSizedBox.js';
// Layout constants
const STATIC_HEIGHT = 1;
const RESERVED_LINE_COUNT = 5; // for tool name, status, padding etc.
const STATUS_INDICATOR_WIDTH = 3;
const MIN_LINES_SHOWN = 2; // show at least this many lines
const CONTENT_PADDING_LEFT = 1;
const RESULT_MARGIN_TOP = 1;
// Large threshold to ensure we don't cause performance issues for very large
// outputs that will get truncated further MaxSizedBox anyway.
const MAXIMUM_RESULT_DISPLAY_CHARACTERS = 1000000;
// Status indicator symbols - more intuitive and visually appealing
const STATUS_SYMBOLS = {
    [ToolCallStatus.Pending]: '◦', // Open circle for waiting/pending
    [ToolCallStatus.Executing]: '◈', // Diamond for active execution
    [ToolCallStatus.Success]: '✓', // Check mark for success
    [ToolCallStatus.Confirming]: '⚡', // Lightning for user action required
    [ToolCallStatus.Canceled]: '⊘', // Crossed circle for canceled
    [ToolCallStatus.Error]: '✗', // X mark for errors
};
// Status colors mapping
const STATUS_COLORS = {
    [ToolCallStatus.Pending]: Colors.AccentBlue,
    [ToolCallStatus.Executing]: Colors.AccentPurple,
    [ToolCallStatus.Success]: Colors.AccentGreen,
    [ToolCallStatus.Confirming]: Colors.AccentYellow,
    [ToolCallStatus.Canceled]: Colors.AccentYellow,
    [ToolCallStatus.Error]: Colors.AccentRed,
};
export const ToolMessage = ({ name, description, resultDisplay, status, availableTerminalHeight, terminalWidth, emphasis = 'medium', renderOutputAsMarkdown = true, }) => {
    const availableHeight = useMemo(() => availableTerminalHeight
        ? Math.max(availableTerminalHeight - STATIC_HEIGHT - RESERVED_LINE_COUNT, MIN_LINES_SHOWN + 1)
        : undefined, [availableTerminalHeight]);
    // Long tool call response in MarkdownDisplay doesn't respect availableTerminalHeight properly,
    // we're forcing it to not render as markdown when the response is too long, it will fallback
    // to render as plain text, which is contained within the terminal using MaxSizedBox
    const shouldRenderAsMarkdown = useMemo(() => availableHeight ? false : renderOutputAsMarkdown, [availableHeight, renderOutputAsMarkdown]);
    const childWidth = useMemo(() => terminalWidth - STATUS_INDICATOR_WIDTH - CONTENT_PADDING_LEFT, [terminalWidth]);
    const processedResultDisplay = useMemo(() => {
        if (typeof resultDisplay !== 'string') {
            return resultDisplay;
        }
        if (resultDisplay.length > MAXIMUM_RESULT_DISPLAY_CHARACTERS) {
            // Truncate the result display to fit within the available width.
            return '...' + resultDisplay.slice(-MAXIMUM_RESULT_DISPLAY_CHARACTERS);
        }
        return resultDisplay;
    }, [resultDisplay]);
    const renderResultContent = useCallback(() => {
        if (!processedResultDisplay)
            return null;
        if (typeof processedResultDisplay === 'string') {
            if (shouldRenderAsMarkdown) {
                return (_jsx(Box, { flexDirection: "column", children: _jsx(MarkdownDisplay, { text: processedResultDisplay, isPending: false, availableTerminalHeight: availableHeight, terminalWidth: childWidth }) }));
            }
            else {
                return (_jsx(MaxSizedBox, { maxHeight: availableHeight, maxWidth: childWidth, children: _jsx(Box, { children: _jsx(Text, { wrap: "wrap", children: processedResultDisplay }) }) }));
            }
        }
        else {
            return (_jsx(DiffRenderer, { diffContent: processedResultDisplay.fileDiff, filename: processedResultDisplay.fileName, availableTerminalHeight: availableHeight, terminalWidth: childWidth }));
        }
    }, [processedResultDisplay, shouldRenderAsMarkdown, availableHeight, childWidth]);
    return (_jsxs(Box, { paddingX: CONTENT_PADDING_LEFT, paddingY: 0, flexDirection: "column", children: [_jsxs(Box, { minHeight: 1, flexDirection: "row", alignItems: "center", children: [_jsx(ToolStatusIndicator, { status: status }), _jsx(Box, { flexGrow: 1, children: _jsx(ToolInfo, { name: name, status: status, description: description, emphasis: emphasis }) }), emphasis === 'high' && _jsx(TrailingIndicator, {})] }), processedResultDisplay && (_jsx(Box, { paddingLeft: STATUS_INDICATOR_WIDTH, width: "100%", marginTop: RESULT_MARGIN_TOP, children: _jsx(Box, { flexDirection: "column", width: "100%", children: renderResultContent() }) }))] }));
};
const ToolStatusIndicator = React.memo(({ status, }) => {
    const renderStatusIcon = useCallback(() => {
        const color = STATUS_COLORS[status];
        const symbol = STATUS_SYMBOLS[status];
        if (status === ToolCallStatus.Executing) {
            return (_jsx(ArienRespondingSpinner, { spinnerType: "toggle", nonRespondingDisplay: symbol }));
        }
        const isBold = status === ToolCallStatus.Success ||
            status === ToolCallStatus.Error ||
            status === ToolCallStatus.Canceled;
        return (_jsx(Text, { color: color, bold: isBold, children: symbol }));
    }, [status]);
    return (_jsx(Box, { minWidth: STATUS_INDICATOR_WIDTH, justifyContent: "flex-start", children: renderStatusIcon() }));
});
ToolStatusIndicator.displayName = 'ToolStatusIndicator';
const ToolInfo = React.memo(({ name, description, status, emphasis, }) => {
    const nameColor = useMemo(() => {
        switch (emphasis) {
            case 'high':
                return Colors.AccentPurple;
            case 'medium':
                return Colors.Foreground;
            case 'low':
                return Colors.Gray;
            default: {
                const exhaustiveCheck = emphasis;
                return exhaustiveCheck;
            }
        }
    }, [emphasis]);
    const getStatusSuffix = useCallback(() => {
        switch (status) {
            case ToolCallStatus.Executing:
                return ' ...';
            case ToolCallStatus.Error:
                return ' (failed)';
            case ToolCallStatus.Canceled:
                return ' (canceled)';
            case ToolCallStatus.Confirming:
                return ' (awaiting approval)';
            default:
                return '';
        }
    }, [status]);
    const statusSuffix = getStatusSuffix();
    const isStrikethrough = status === ToolCallStatus.Canceled;
    const isDimmed = emphasis === 'low';
    return (_jsx(Box, { flexDirection: "row", flexGrow: 1, children: _jsxs(Text, { wrap: "truncate-end", strikethrough: isStrikethrough, children: [_jsx(Text, { color: nameColor, bold: true, children: name }), _jsxs(Text, { color: Colors.Gray, dimColor: isDimmed, children: [' ', description, statusSuffix] })] }) }));
});
ToolInfo.displayName = 'ToolInfo';
const TrailingIndicator = React.memo(() => (_jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: Colors.Foreground, wrap: "truncate", children: "\u2190" }) })));
TrailingIndicator.displayName = 'TrailingIndicator';
//# sourceMappingURL=ToolMessage.js.map