import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';
export const UserMessage = ({ text }) => {
    const prefix = '>';
    return (_jsxs(Box, { flexDirection: "row", marginTop: 1, marginBottom: 1, children: [_jsx(Box, { width: 2, marginRight: 1, children: _jsx(Text, { color: Colors.AccentGreen, bold: true, children: prefix }) }), _jsx(Box, { flexGrow: 1, paddingX: 1, children: _jsx(Text, { wrap: "wrap", color: Colors.Foreground, bold: true, children: text }) })] }));
};
//# sourceMappingURL=UserMessage.js.map