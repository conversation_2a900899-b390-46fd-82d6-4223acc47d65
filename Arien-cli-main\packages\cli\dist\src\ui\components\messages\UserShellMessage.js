import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';
export const UserShellMessage = ({ text }) => {
    // Remove leading '!' if present, as App.tsx adds it for the processor.
    const commandToDisplay = text.startsWith('!') ? text.substring(1) : text;
    return (_jsxs(Box, { flexDirection: "row", marginBottom: 0, children: [_jsx(Box, { width: 2, marginRight: 1, children: _jsx(Text, { color: Colors.AccentCyan, bold: true, children: "$ " }) }), _jsx(Box, { flexGrow: 1, children: _jsx(Text, { children: commandToDisplay }) })] }));
};
//# sourceMappingURL=UserShellMessage.js.map