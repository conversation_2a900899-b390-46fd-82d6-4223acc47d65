/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
interface SeparatorProps {
    width?: number;
    character?: string;
    color?: string;
    dimColor?: boolean;
    marginTop?: number;
    marginBottom?: number;
}
export declare const Separator: React.FC<SeparatorProps>;
interface DottedSeparatorProps {
    width?: number;
    color?: string;
    marginTop?: number;
    marginBottom?: number;
}
export declare const DottedSeparator: React.FC<DottedSeparatorProps>;
interface SectionSeparatorProps {
    title?: string;
    width?: number;
    color?: string;
    marginTop?: number;
    marginBottom?: number;
}
export declare const SectionSeparator: React.FC<SectionSeparatorProps>;
export {};
