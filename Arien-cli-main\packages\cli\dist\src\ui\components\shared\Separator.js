import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';
export const Separator = ({ width = 50, character = '─', color = Colors.Gray, dimColor = true, marginTop = 0, marginBottom = 0, }) => (_jsx(Box, { marginTop: marginTop, marginBottom: marginBottom, children: _jsx(Text, { color: color, dimColor: dimColor, children: character.repeat(width) }) }));
export const DottedSeparator = ({ width = 50, color = Colors.Gray, marginTop = 0, marginBottom = 0, }) => {
    const pattern = '· ';
    const count = Math.floor(width / pattern.length);
    return (_jsx(Box, { marginTop: marginTop, marginBottom: marginBottom, children: _jsx(Text, { color: color, dimColor: true, children: pattern.repeat(count) }) }));
};
export const SectionSeparator = ({ title, width = 50, color = Colors.AccentCyan, marginTop = 1, marginBottom = 1, }) => {
    if (!title) {
        return _jsx(Separator, { width: width, color: color, marginTop: marginTop, marginBottom: marginBottom });
    }
    const titleWithSpaces = ` ${title} `;
    const titleWidth = titleWithSpaces.length;
    const sideWidth = Math.max(1, Math.floor((width - titleWidth) / 2));
    return (_jsx(Box, { marginTop: marginTop, marginBottom: marginBottom, children: _jsxs(Text, { color: color, children: ['─'.repeat(sideWidth), _jsx(Text, { bold: true, children: titleWithSpaces }), '─'.repeat(sideWidth)] }) }));
};
//# sourceMappingURL=Separator.js.map