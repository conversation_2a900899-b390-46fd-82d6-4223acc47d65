{"version": 3, "file": "useHistoryManager.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useHistoryManager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAmBtD;;;;;GAKG;AACH,MAAM,UAAU,UAAU;IACxB,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAgB,EAAE,CAAC,CAAC;IAC1D,MAAM,mBAAmB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAEtC,oEAAoE;IACpE,MAAM,gBAAgB,GAAG,WAAW,CAAC,CAAC,aAAqB,EAAU,EAAE;QACrE,mBAAmB,CAAC,OAAO,IAAI,CAAC,CAAC;QACjC,OAAO,aAAa,GAAG,mBAAmB,CAAC,OAAO,CAAC;IACrD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,UAAyB,EAAE,EAAE;QAC5D,UAAU,CAAC,UAAU,CAAC,CAAC;IACzB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,yDAAyD;IACzD,MAAM,OAAO,GAAG,WAAW,CACzB,CAAC,QAAiC,EAAE,aAAqB,EAAU,EAAE;QACnE,MAAM,EAAE,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAgB,EAAE,GAAG,QAAQ,EAAE,EAAE,EAAiB,CAAC;QAEhE,UAAU,CAAC,CAAC,WAAW,EAAE,EAAE;YACzB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAErD,+BAA+B;gBAE/B,yFAAyF;gBACzF,2FAA2F;gBAC3F,IACE,QAAQ,CAAC,IAAI,KAAK,MAAM;oBACxB,OAAO,CAAC,IAAI,KAAK,MAAM;oBACvB,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;oBAC9B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,uCAAuC;kBACxE,CAAC;oBACD,OAAO,WAAW,CAAC,CAAC,0BAA0B;gBAChD,CAAC;gBAED,sDAAsD;gBACtD,IACE,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC;oBAChE,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,eAAe,CAAC;oBAC9D,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAC9B,CAAC;oBACD,OAAO,WAAW,CAAC,CAAC,0BAA0B;gBAChD,CAAC;gBAED,mEAAmE;gBACnE,IACE,QAAQ,CAAC,IAAI,KAAK,MAAM;oBACxB,OAAO,CAAC,IAAI,KAAK,MAAM;oBACvB,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;oBAC9B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB;kBACpD,CAAC;oBACD,OAAO,WAAW,CAAC,CAAC,0BAA0B;gBAChD,CAAC;gBAED,oEAAoE;gBACpE,IACE,QAAQ,CAAC,IAAI,KAAK,OAAO;oBACzB,OAAO,CAAC,IAAI,KAAK,OAAO;oBACxB,QAAQ,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;oBAC9B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,8BAA8B;kBAChE,CAAC;oBACD,OAAO,WAAW,CAAC,CAAC,0BAA0B;gBAChD,CAAC;gBAED,iEAAiE;gBACjE,IACE,QAAQ,CAAC,IAAI,KAAK,YAAY;oBAC9B,OAAO,CAAC,IAAI,KAAK,YAAY;oBAC7B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAChE,CAAC;oBACD,OAAO,WAAW,CAAC,CAAC,0BAA0B;gBAChD,CAAC;gBAED,gEAAgE;gBAChE,yEAAyE;gBACzE,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC7C,MAAM,mBAAmB,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACpD,wFAAwF;oBACxF,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,OAAO;wBACnC,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM;wBAC/C,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;wBAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,yCAAyC;oBACjF,CAAC;oBAED,wDAAwD;oBACxD,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,OAAO;wBACnC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;wBAC1B,IAAI,CAAC,IAAI,KAAK,MAAM;wBACpB,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;wBAC/B,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,2CAA2C;oBAC3C,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;wBAC1B,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC;wBAC9C,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,aAAa,EAAE,GAAG,IAAI,CAAC;wBAC/C,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,gBAAgB,EAAE,GAAG,OAAO,CAAC;wBACxD,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;oBAC5E,CAAC;oBAED,OAAO,KAAK,CAAC;gBACf,CAAC,CAAC,CAAC;gBAEH,IAAI,mBAAmB,EAAE,CAAC;oBACxB,OAAO,WAAW,CAAC,CAAC,0BAA0B;gBAChD,CAAC;YACH,CAAC;YAED,OAAO,CAAC,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,OAAO,EAAE,CAAC,CAAC,iEAAiE;IAC9E,CAAC,EACD,CAAC,gBAAgB,CAAC,CACnB,CAAC;IAEF;;;;;OAKG;IACH,EAAE;IACF,MAAM,UAAU,GAAG,WAAW,CAC5B,CACE,EAAU,EACV,OAA8D,EAC9D,EAAE;QACF,UAAU,CAAC,CAAC,WAAW,EAAE,EAAE,CACzB,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACvB,IAAI,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACnB,8DAA8D;gBAC9D,MAAM,UAAU,GACd,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;gBAC1D,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,UAAU,EAAiB,CAAC;YACnD,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,EACD,EAAE,CACH,CAAC;IAEF,6DAA6D;IAC7D,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;QAClC,UAAU,CAAC,EAAE,CAAC,CAAC;QACf,mBAAmB,CAAC,OAAO,GAAG,CAAC,CAAC;IAClC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW;KACZ,CAAC;AACJ,CAAC"}