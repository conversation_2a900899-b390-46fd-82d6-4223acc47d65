/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * System clipboard utility that provides safe access to the system clipboard
 * with proper error handling and fallback behavior.
 */
export declare class SystemClipboard {
    private static instance;
    private isAvailable;
    private constructor();
    /**
     * Get the singleton instance of SystemClipboard
     */
    static getInstance(): SystemClipboard;
    /**
     * Test if the system clipboard is available
     */
    private testClipboardAvailability;
    /**
     * Write text to the system clipboard
     * @param text The text to write to clipboard
     * @returns Promise<boolean> True if successful, false otherwise
     */
    writeText(text: string): Promise<boolean>;
    /**
     * Read text from the system clipboard
     * @returns Promise<string | null> The clipboard text or null if unavailable/error
     */
    readText(): Promise<string | null>;
    /**
     * Check if the system clipboard is available
     * @returns boolean True if clipboard is available
     */
    isClipboardAvailable(): boolean;
    /**
     * Retry clipboard availability test
     */
    retryAvailabilityTest(): Promise<void>;
}
/**
 * Get the singleton system clipboard instance
 */
export declare const systemClipboard: SystemClipboard;
