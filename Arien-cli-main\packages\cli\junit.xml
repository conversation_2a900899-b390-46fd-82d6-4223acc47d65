<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="6" failures="0" errors="0" time="0.0683689">
    <testsuite name="src/ui/components/messages/ArienMessage.test.tsx" timestamp="2025-07-02T17:46:04.741Z" hostname="Ajayk" tests="6" failures="0" errors="0" skipped="0" time="0.0683689">
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should render without crashing" time="0.041924">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should display message text in MarkdownDisplay" time="0.0107458">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should display message with left border indicator" time="0.0042715">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should handle pending state correctly" time="0.0034189">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should pass availableTerminalHeight to MarkdownDisplay when provided" time="0.0032295">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should pass terminalWidth to MarkdownDisplay" time="0.002614">
        </testcase>
    </testsuite>
</testsuites>
