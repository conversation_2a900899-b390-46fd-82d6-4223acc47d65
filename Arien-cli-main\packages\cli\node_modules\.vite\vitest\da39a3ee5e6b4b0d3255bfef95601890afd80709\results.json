{"version": "3.2.4", "results": [[":src/ui/hooks/slashCommandProcessor.test.ts", {"duration": 397.9978999999994, "failed": true}], [":src/ui/components/shared/text-buffer.test.ts", {"duration": 797.7521999999999, "failed": false}], [":src/ui/hooks/useArienStream.test.tsx", {"duration": 623.0644000000002, "failed": false}], [":src/ui/hooks/useToolScheduler.test.ts", {"duration": 335.10080000000016, "failed": false}], [":src/ui/hooks/atCommandProcessor.test.ts", {"duration": 96.26229999999941, "failed": true}], [":src/config/settings.test.ts", {"duration": 80.24299999999948, "failed": false}], [":src/config/config.test.ts", {"duration": 53800.0225, "failed": true}], [":src/ui/App.test.tsx", {"duration": 1245.7456999999995, "failed": true}], [":src/ui/hooks/useCompletion.integration.test.ts", {"duration": 1519.786399999999, "failed": true}], [":src/ui/components/messages/DiffRenderer.test.tsx", {"duration": 614.3435, "failed": false}], [":src/ui/hooks/useAutoAcceptIndicator.test.ts", {"duration": 115.64740000000074, "failed": false}], [":src/ui/components/shared/MaxSizedBox.test.tsx", {"duration": 201.45380000000023, "failed": false}], [":src/nonInteractiveCli.test.ts", {"duration": 37.07330000000002, "failed": false}], [":src/ui/components/messages/ToolMessage.test.tsx", {"duration": 215.90989999999965, "failed": false}], [":src/ui/hooks/useHistoryManager.test.ts", {"duration": 162.85709999999972, "failed": true}], [":src/config/mcp-integration.test.ts", {"duration": 16386.0678, "failed": true}], [":src/config/config.integration.test.ts", {"duration": 167.58269999999993, "failed": false}], [":src/ui/hooks/useEditorSettings.test.ts", {"duration": 142.1318000000001, "failed": false}], [":src/ui/contexts/SessionContext.test.tsx", {"duration": 140.1796000000004, "failed": false}], [":src/ui/hooks/useInputHistory.test.ts", {"duration": 91.2844, "failed": false}], [":src/ui/hooks/useGitBranchName.test.ts", {"duration": 159.4273000000003, "failed": false}], [":src/ui/hooks/useShellHistory.test.ts", {"duration": 892.4387999999999, "failed": false}], [":src/config/built-in-mcp-servers.test.ts", {"duration": 74.60310000000027, "failed": true}], [":src/ui/components/LoadingIndicator.test.tsx", {"duration": 149.38400000000001, "failed": false}], [":src/ui/components/InputPrompt.test.tsx", {"duration": 879.7380999999996, "failed": false}], [":src/ui/hooks/useConsoleMessages.test.ts", {"duration": 107.17970000000014, "failed": false}], [":src/ui/hooks/usePhraseCycler.test.ts", {"duration": 94.76300000000037, "failed": false}], [":src/ui/hooks/shellCommandProcessor.test.ts", {"duration": 77.95450000000028, "failed": false}], [":src/ui/utils/errorParsing.test.ts", {"duration": 13.545100000000275, "failed": false}], [":src/ui/hooks/useLoadingIndicator.test.ts", {"duration": 130.74749999999995, "failed": false}], [":src/arien.test.tsx", {"duration": 12.141200000000026, "failed": false}], [":src/ui/hooks/useTimer.test.ts", {"duration": 94.33470000000034, "failed": false}], [":src/config/extension.test.ts", {"duration": 50.59609999999975, "failed": false}], [":src/ui/components/AuthDialog.test.tsx", {"duration": 363.7681999999995, "failed": false}], [":src/ui/components/messages/ArienMessage.test.tsx", {"duration": 68.36889999999994, "failed": false}], [":src/ui/components/HistoryItemDisplay.test.tsx", {"duration": 332.3591000000006, "failed": true}], [":src/utils/startupWarnings.test.ts", {"duration": 0, "failed": false}], [":src/ui/components/Stats.test.tsx", {"duration": 138.77230000000054, "failed": false}], [":src/ui/utils/formatters.test.ts", {"duration": 16.37609999999995, "failed": false}], [":src/ui/utils/markdownUtilities.test.ts", {"duration": 11.159500000000207, "failed": false}], [":src/ui/components/StatsDisplay.test.tsx", {"duration": 131.1286, "failed": false}], [":src/ui/components/messages/ToolConfirmationMessage.test.tsx", {"duration": 55.782100000000355, "failed": false}], [":src/ui/components/SessionSummaryDisplay.test.tsx", {"duration": 149.61180000000058, "failed": false}], [":src/ui/utils/textUtils.test.ts", {"duration": 10.415199999999913, "failed": false}]]}