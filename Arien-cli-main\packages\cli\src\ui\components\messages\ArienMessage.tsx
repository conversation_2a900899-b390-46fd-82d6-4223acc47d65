/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { Colors } from '../../colors.js';

interface ArienMessageProps {
  text: string;
  isPending: boolean;
  availableTerminalHeight?: number;
  terminalWidth: number;
}

export const ArienMessage: React.FC<ArienMessageProps> = ({
  text,
  isPending,
  availableTerminalHeight,
  terminalWidth,
}) => {
  // Calculate available width for content with left border indicator
  const contentWidth = Math.max(20, terminalWidth - 4);

  return (
    <Box flexDirection="column" marginTop={1} marginBottom={2}>
      <Box flexDirection="row">
        {/* Left border indicator */}
        <Box marginRight={1}>
          <Text color={Colors.AccentPurple}>│</Text>
        </Box>
        {/* Content area with subtle background */}
        <Box
          flexGrow={1}
          paddingX={2}
          paddingY={1}
          borderStyle="round"
          borderColor={Colors.Gray}
        >
          <MarkdownDisplay
            text={text}
            isPending={isPending}
            availableTerminalHeight={availableTerminalHeight}
            terminalWidth={contentWidth}
          />
        </Box>
      </Box>
    </Box>
  );
};