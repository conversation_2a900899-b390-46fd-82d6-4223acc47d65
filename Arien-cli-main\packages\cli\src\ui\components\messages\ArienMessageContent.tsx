/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { MarkdownDisplay } from '../../utils/MarkdownDisplay.js';
import { Colors } from '../../colors.js';

interface ArienMessageContentProps {
  text: string;
  isPending: boolean;
  availableTerminalHeight?: number;
  terminalWidth: number;
}

/*
 * Arien message content is a semi-hacked component. The intention is to represent a partial
 * of ArienMessage and is only used when a response gets too long. In that instance messages
 * are split into multiple ArienMessageContent's to enable the root <Static> component in
 * App.tsx to be as performant as humanly possible.
 */
export const ArienMessageContent: React.FC<ArienMessageContentProps> = ({
  text,
  isPending,
  availableTerminalHeight,
  terminalWidth,
}) => {
  // Calculate available width for content with left border indicator
  const contentWidth = Math.max(20, terminalWidth - 4);

  return (
    // Use the same styling as ArienMessage for consistent alignment
    <Box flexDirection="column">
      <Box flexDirection="row">
        {/* Left border indicator */}
        <Box marginRight={1}>
          <Text color={Colors.AccentPurple}>│</Text>
        </Box>
        {/* Content area with subtle background */}
        <Box
          flexGrow={1}
          paddingX={2}
          paddingY={1}
          borderStyle="round"
          borderColor={Colors.Gray}
        >
          <MarkdownDisplay
            text={text}
            isPending={isPending}
            availableTerminalHeight={availableTerminalHeight}
            terminalWidth={contentWidth}
          />
        </Box>
      </Box>
    </Box>
  );
};