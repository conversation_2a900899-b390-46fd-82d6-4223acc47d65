/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Text, Box } from 'ink';
import { Colors } from '../../colors.js';

interface UserMessageProps {
  text: string;
}

export const UserMessage: React.FC<UserMessageProps> = ({ text }) => {
  const prefix = '>';

  return (
    <Box flexDirection="row" marginTop={1} marginBottom={1}>
      <Box width={2} marginRight={1}>
        <Text color={Colors.AccentGreen} bold>{prefix}</Text>
      </Box>
      <Box flexGrow={1} paddingX={1}>
        <Text wrap="wrap" color={Colors.Foreground} bold>
          {text}
        </Text>
      </Box>
    </Box>
  );
};
