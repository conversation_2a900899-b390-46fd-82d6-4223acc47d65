/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Colors } from '../../colors.js';

interface UserShellMessageProps {
  text: string;
}

export const UserShellMessage: React.FC<UserShellMessageProps> = ({ text }) => {
  // Remove leading '!' if present, as App.tsx adds it for the processor.
  const commandToDisplay = text.startsWith('!') ? text.substring(1) : text;

  return (
    <Box flexDirection="row" marginBottom={0}>
      <Box width={2} marginRight={1}>
        <Text color={Colors.AccentCyan} bold>$ </Text>
      </Box>
      <Box flexGrow={1}>
        <Text>{commandToDisplay}</Text>
      </Box>
    </Box>
  );
};
