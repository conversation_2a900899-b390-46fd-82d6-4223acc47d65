/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { useState, useRef, useCallback } from 'react';
import { HistoryItem } from '../types.js';

// Type for the updater function passed to updateHistoryItem
type HistoryItemUpdater = (
  prevItem: HistoryItem,
) => Partial<Omit<HistoryItem, 'id'>>;

export interface UseHistoryManagerReturn {
  history: HistoryItem[];
  addItem: (itemData: Omit<HistoryItem, 'id'>, baseTimestamp: number) => number; // Returns the generated ID
  updateItem: (
    id: number,
    updates: Partial<Omit<HistoryItem, 'id'>> | HistoryItemUpdater,
  ) => void;
  clearItems: () => void;
  loadHistory: (newHistory: HistoryItem[]) => void;
}

/**
 * Custom hook to manage the chat history state.
 *
 * Encapsulates the history array, message ID generation, adding items,
 * updating items, and clearing the history.
 */
export function useHistory(): UseHistoryManagerReturn {
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const messageIdCounterRef = useRef(0);

  // Generates a unique message ID based on a timestamp and a counter.
  const getNextMessageId = useCallback((baseTimestamp: number): number => {
    messageIdCounterRef.current += 1;
    return baseTimestamp + messageIdCounterRef.current;
  }, []);

  const loadHistory = useCallback((newHistory: HistoryItem[]) => {
    setHistory(newHistory);
  }, []);

  // Adds a new item to the history state with a unique ID.
  const addItem = useCallback(
    (itemData: Omit<HistoryItem, 'id'>, baseTimestamp: number): number => {
      const id = getNextMessageId(baseTimestamp);
      const newItem: HistoryItem = { ...itemData, id } as HistoryItem;

      setHistory((prevHistory) => {
        if (prevHistory.length > 0) {
          const lastItem = prevHistory[prevHistory.length - 1];
          
          // Enhanced deduplication logic

          // Prevent adding duplicate consecutive user messages only within a very short time frame
          // This allows intentional repeated messages while preventing accidental double-submissions
          if (
            lastItem.type === 'user' &&
            newItem.type === 'user' &&
            lastItem.text === newItem.text &&
            Math.abs(lastItem.id - id) < 500 // Only prevent duplicates within 500ms
          ) {
            return prevHistory; // Don't add the duplicate
          }
          
          // Prevent adding duplicate consecutive arien messages
          if (
            (lastItem.type === 'arien' || lastItem.type === 'arien_content') &&
            (newItem.type === 'arien' || newItem.type === 'arien_content') &&
            lastItem.text === newItem.text
          ) {
            return prevHistory; // Don't add the duplicate
          }
          
          // Prevent adding duplicate info messages within a short time frame
          if (
            lastItem.type === 'info' &&
            newItem.type === 'info' &&
            lastItem.text === newItem.text &&
            Math.abs(lastItem.id - id) < 1000 // Within 1 second
          ) {
            return prevHistory; // Don't add the duplicate
          }
          
          // Prevent adding duplicate error messages within a short time frame
          if (
            lastItem.type === 'error' &&
            newItem.type === 'error' &&
            lastItem.text === newItem.text &&
            Math.abs(lastItem.id - id) < 2000 // Within 2 seconds for errors
          ) {
            return prevHistory; // Don't add the duplicate
          }
          
          // Prevent adding duplicate tool group messages with same content
          if (
            lastItem.type === 'tool_group' &&
            newItem.type === 'tool_group' &&
            JSON.stringify(lastItem.tools) === JSON.stringify(newItem.tools)
          ) {
            return prevHistory; // Don't add the duplicate
          }
          
          // Check for global duplicates in recent history (last 10 items)
          // But be more lenient with user messages to allow intentional repetition
          const recentHistory = prevHistory.slice(-10);
          const isDuplicateInRecent = recentHistory.some(item => {
            // For user messages, only consider it a duplicate if it's very recent (within 1 second)
            if ('text' in item && 'text' in newItem &&
                item.type === 'user' && newItem.type === 'user' &&
                item.text === newItem.text) {
              return Math.abs(item.id - id) < 1000; // Only within 1 second for user messages
            }

            // For non-user text-based items, use the original logic
            if ('text' in item && 'text' in newItem &&
                item.type === newItem.type &&
                item.type !== 'user' &&
                item.text === newItem.text) {
              return true;
            }

            // For structured items, do deep comparison
            if (item.type === newItem.type &&
                !('text' in item) && !('text' in newItem)) {
              const { id: _itemId, ...itemWithoutId } = item;
              const { id: _newItemId, ...newItemWithoutId } = newItem;
              return JSON.stringify(itemWithoutId) === JSON.stringify(newItemWithoutId);
            }

            return false;
          });

          if (isDuplicateInRecent) {
            return prevHistory; // Don't add the duplicate
          }
        }
        
        return [...prevHistory, newItem];
      });
      return id; // Return the generated ID (even if not added, to keep signature)
    },
    [getNextMessageId],
  );

  /**
   * Updates an existing history item identified by its ID.
   * @deprecated Prefer not to update history item directly as we are currently
   * rendering all history items in <Static /> for performance reasons. Only use
   * if ABSOLUTELY NECESSARY
   */
  //
  const updateItem = useCallback(
    (
      id: number,
      updates: Partial<Omit<HistoryItem, 'id'>> | HistoryItemUpdater,
    ) => {
      setHistory((prevHistory) =>
        prevHistory.map((item) => {
          if (item.id === id) {
            // Apply updates based on whether it's an object or a function
            const newUpdates =
              typeof updates === 'function' ? updates(item) : updates;
            return { ...item, ...newUpdates } as HistoryItem;
          }
          return item;
        }),
      );
    },
    [],
  );

  // Clears the entire history state and resets the ID counter.
  const clearItems = useCallback(() => {
    setHistory([]);
    messageIdCounterRef.current = 0;
  }, []);

  return {
    history,
    addItem,
    updateItem,
    clearItems,
    loadHistory,
  };
}
